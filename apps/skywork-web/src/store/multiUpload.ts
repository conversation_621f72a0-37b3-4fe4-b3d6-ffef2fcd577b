import { defineStore } from "pinia";
import { ref } from "vue";

export interface MultiUploadHomeItem {
  name: string;
  isTemplate?: boolean;
  icon: string;
}

export const useMultiUploadStore = defineStore("multiUpload", () => {
  const fileList = ref<MultiUploadHomeItem[]>([]);

  const deleteFile = (name: string) => {
    fileList.value = fileList.value.filter((file) => file.name !== name);
  };

  return {
    fileList,
    deleteFile,
  };
});
