import { defineStore } from "pinia";
import { ref } from "vue";

interface SpaceInfo {
  file_id: string;
  type: string;
  file_name: string;
  file_type: string;
  url: string;
  preview_url: string;
  thumbnail_url: string;
  created: string;
  icon: string;
  name: string;
}

interface FileItem {
  parentId: number;
  id: number;
  label: string;
  expanded: boolean;
  level: number;
  checked: boolean;
  space: SpaceInfo;
}

export const useUploadKnowledgeStore = defineStore("uploadKnowledge", () => {
  const fileList = ref<FileItem[]>([]);

  const setFileList = (list: FileItem[]) => {
    list.forEach((newItem) => {
      const existingIndex = fileList.value.findIndex((item) => item.space.file_id === newItem.space.file_id);
      if (existingIndex !== -1) {
        fileList.value.splice(existingIndex, 1); // 删除旧文件
      }
      fileList.value.push(newItem); // 追加新文件
    });
  };

  return {
    fileList,
    setFileList,
  };
});
