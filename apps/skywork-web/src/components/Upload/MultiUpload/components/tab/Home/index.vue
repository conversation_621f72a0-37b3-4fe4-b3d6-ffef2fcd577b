<script lang="ts" setup>
import { isDefaultTab } from "@/components/Upload/MultiUpload/utils/isDefaultTab";
import UploadButtonGroup from "@/components/Upload/MultiUpload/components/tab/Home/components/UploadButtonGroup.vue";
import FileList, { MultiUploadHomeItem } from "./components/FileList.vue";
import { ElButton } from "element-plus";
import { useUploadKnowledgeStore } from "@/components/Upload/MultiUploadWithDialog/store/uploadKnowledgeStore";
import { storeToRefs } from "pinia";

interface Props {
  currentComponent: string;
  setCurrentComponent: (component: string) => void;
}

const { currentComponent, setCurrentComponent } = defineProps<Props>();

const uploadKnowledgeStore = useUploadKnowledgeStore();
const { fileList } = storeToRefs(uploadKnowledgeStore);

const transformedFileList = computed(() => {
  return fileList.value.map((item) => ({
    name: `${item.space?.file_name || "未知文件"}.${item.space?.file_type || ""}`,
    isTemplate: false,
    icon: item.space?.icon || "ic_files_doc",
  }));
});
</script>

<template>
  <div>
    <UploadButtonGroup
      v-if="isDefaultTab(currentComponent)"
      :setCurrentComponent="setCurrentComponent"
    ></UploadButtonGroup>
    <div class="mb-[16px] h-[356px] overflow-y-auto">
      <FileList :list="transformedFileList" />
    </div>

    <div class="flex justify-end pt-5">
      <ElButton color="black">确定</ElButton>
    </div>
  </div>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 3px; /* 滚动条宽度 */
  border-radius: 31px;
}
::-webkit-scrollbar-thumb {
  background: var(--fill-fill-1, #d4d6dc);
}
</style>
