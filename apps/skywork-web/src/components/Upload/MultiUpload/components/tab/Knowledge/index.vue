<script lang="ts" setup>
import { useUploadKnowledgeStore } from "@/components/Upload/MultiUploadWithDialog/store/uploadKnowledgeStore";

import { useTemplateRef } from "vue";
import UploadKnowledge from "../../upload/UploadKnowledge/index.vue";

interface Props {
  currentComponent: string;
  setCurrentComponent: (component: string) => void;
}

const { setCurrentComponent } = defineProps<Props>();

const from = "home";
const uploadFrom = "project_upload";

const knowledgeSearchRef = useTemplateRef("knowledgeSearch");
const uploadKnowledgeStore = useUploadKnowledgeStore();

const flattenTree = (nodes) => {
  const result: any[] = [];
  const traverse = (nodeList) => {
    nodeList.forEach((node) => {
      result.push(node);
      if (node.children) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return result;
};
const handleClick = () => {
  const list = flattenTree(knowledgeSearchRef.value?.searchResult);
  const selectedList = list.filter((item) => item.checked);

  uploadKnowledgeStore.setFileList(selectedList);

  setCurrentComponent("home");
};
</script>

<template>
  <div>
    <UploadKnowledge class="!h-[428px] overflow-y-auto" ref="knowledgeSearch" :knowsearch_from="uploadFrom" />
    <div class="flex justify-end pt-6">
      <ElButton color="black" @click="handleClick">确定</ElButton>
    </div>
  </div>
</template>
